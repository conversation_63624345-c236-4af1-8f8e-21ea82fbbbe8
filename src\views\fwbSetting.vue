<template>
  <el-card class="specification-container">
    <template #header>
      <div class="header">
        <el-button type="primary" :icon="Plus" @click="handleAdd">添加商品规格</el-button>
      </div>
    </template>
    <el-table
      :loading="state.loading"
      :data="processTableData(state.tableData)"
      tooltip-effect="dark"
      style="width: 100%"
    >
      <el-table-column
        prop="specName"
        label="规格名称"
        width="180"
      >
      </el-table-column>
      <el-table-column
        label="规格属性"
      >
        <template #default="scope">
          <div v-for="(attrGroup, attrIndex) in scope.row.attributes" :key="attrIndex" class="attr-group">
            <div class="attr-type">{{ attrGroup.type }}:</div>
            <div class="attr-values">
              <el-tag
                v-for="(attr, i) in attrGroup.values"
                :key="i"
                style="margin-right: 5px; margin-bottom: 5px;"
                type="info"
                effect="light"
              >
                {{ attr.attribute }}
              </el-tag>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="180"
      >
        <template #default="scope">
          <el-button type="primary" link @click="handleEdit(scope.row.originalData)">修改</el-button>
          <el-button type="danger" link @click="handleDelete(scope.row.specName)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="state.total"
      :page-size="state.pageSize"
      :current-page="state.pageNumber"
      @current-change="changePage"
    />
  </el-card>
  <DialogAddSpecification
    ref="addSpecification"
    :reload="getSpecificationList"
    :type="state.type"
    @success="getSpecificationList"
    @close="getSpecificationList"
  />
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import DialogAddSpecification from '@/components/DialogAddSpecification.vue'
import { getSpecificationList as fetchSpecList, deleteSpecification } from '@/api/specification'

const addSpecification = ref(null)
const state = reactive({
  loading: false,
  tableData: [], // 数据列表
  total: 0, // 总条数
  pageNumber: 1, // 当前页
  pageSize: 10, // 分页大小
  type: 'add', // 操作类型：add-添加，edit-编辑
  editData: null // 编辑时的数据
})

// 初始化
onMounted(() => {
  getSpecificationList()
})

// 获取商品规格列表
const getSpecificationList = () => {
  state.loading = true

  fetchSpecList({
    pageNumber: state.pageNumber,
    pageSize: state.pageSize
  }).then(res => {
    if (res && res.list && res.list.length > 0) {
      state.tableData = res.list
      state.total = res.totalCount
      state.pageNumber = res.currPage
    } else {
      state.tableData = []
      state.total = 0
    }
    state.loading = false
    // console.log('规格数据:', state.tableData)
  }).catch(err => {
    console.error('获取规格列表失败:', err)
    state.loading = false
    state.tableData = []
    state.total = 0
  })
}

// 处理表格数据，将嵌套结构转换为扁平结构
const processTableData = (data) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    return []
  }

  const result = []

  // 处理每个规格项
  data.forEach(item => {
    // 遍历每个规格名称
    for (const specName in item) {
      const specData = item[specName]
      const processedItem = {
        specName: specName,
        attributes: [],
        sequence: null,
        originalData: { [specName]: specData } // 保存原始数据用于编辑
      }

      // 遍历该规格下的所有属性类型
      for (const attrType in specData) {
        const attrGroup = {
          type: attrType,
          values: specData[attrType]
        }

        // 保存第一个属性的sequence用于删除操作
        if (!processedItem.sequence && specData[attrType] && specData[attrType].length > 0) {
          processedItem.sequence = specData[attrType][0].sequence
        }

        processedItem.attributes.push(attrGroup)
      }

      result.push(processedItem)
    }
  })
  // console.log('处理后的表格数据:', result)
  return result
}

// 添加商品规格
const handleAdd = () => {
  state.type = 'add'
  state.editData = null
  addSpecification.value.open()
}

// 修改商品规格
const handleEdit = (data) => {
  state.type = 'edit'
  state.editData = data
  // console.log('传递给对话框的数据:', data)
  addSpecification.value.open(data)
}

// 删除商品规格
const handleDelete = (specName) => {
  if (!specName) {
    ElMessage.error('无法获取名称')
    return
  }

  ElMessageBox.confirm('确认要删除该规格吗？', '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteSpecification(specName).then(() => {
      ElMessage.success('删除成功')
      getSpecificationList()
    }).catch(err => {
      console.error('删除规格失败:', err)
      ElMessage.error('删除失败')
    })
  }).catch(() => {})
}

// 翻页方法
const changePage = (val) => {
  state.pageNumber = val
  getSpecificationList()
}
</script>

<style scoped>
.specification-container {
  min-height: 100%;
}
.header {
  display: flex;
  justify-content: space-between;
}
.attr-group {
  margin-bottom: 10px;
  display: flex;
  align-items: flex-start;
}
.attr-type {
  font-weight: bold;
  margin-right: 10px;
  min-width: 60px;
}
.attr-values {
  display: flex;
  flex-wrap: wrap;
}
</style>
