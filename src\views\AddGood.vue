<template>
  
  <div class="add">
    <el-tabs 
      v-model="activeName" 
      type="border-card" 
      class="fixed-tabs"
      @tab-click="handleTabClick"
    >
      <el-tab-pane label="商品信息" name="1" />
      <el-tab-pane label="商品设置" name="2" />
    </el-tabs>
    <el-card v-show="activeName=== '1'" class="add-container">
      <el-form :model="state.goodForm" :rules="state.rules" ref="goodRef" label-width="100px" class="goodForm">
        
        <el-form-item label="商品名称" prop="goodsName">
          <el-input style="width: 300px" v-model="state.goodForm.goodsName" placeholder="请输入商品名称"></el-input>
        </el-form-item>
        <el-form-item label="商品副名称" prop="goodsSubName">
          <el-input style="width: 300px" v-model="state.goodForm.goodsSubName" placeholder="请输入商品副名称"></el-input>
        </el-form-item>
        <el-form-item  label="商品分类" prop="categoryId">
          <el-cascader clearable filterable  
          reserve-keyword ref="cascaderRef"  v-model="selectedValuecategory" :show-all-levels="false" :placeholder="state.defaultCate" style="width: 300px" :props="state.category" @change="handleChangeCate"></el-cascader>
        </el-form-item>
        <el-form-item  label="所属类型"  prop="selectedId">
          <el-select clearable filterable  
        reserve-keyword  placeholder="请选择商品归属"  v-model="selectedId" :options="options" style="width: 300px; margin-right: 10px">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>       
       </el-form-item>
        <el-form-item  label="所属商户" prop="goodsBelongMerchantId" >
          <el-select clearable filterable 
        reserve-keyword  placeholder="请选择商户" 
        v-model="selectedValuemerchants" style="width: 300px; margin-right: 10px">
          <el-option
            v-for="item in optionsmerchants"
            :key="item.merchantId"
            :label="item.merchantName"
            :value="item.merchantId"
          />
        </el-select>

        </el-form-item>
        <el-form-item  label="所属机构" prop="goodsBelongOrganId">
          <el-select clearable filterable 
        reserve-keyword  placeholder="请选择所属机构" 
        v-model="selectedValueorgans" style="width: 300px; margin-right: 10px">
          <el-option
            v-for="item in optionsorgans"
            :key="item.organId"
            :label="item.organName"
            :value="item.organId"
          />
        </el-select>

        </el-form-item>
        <el-form-item label="商品介绍" prop="goodsIntro">
          <el-input style="width: 300px" type="textarea" v-model="state.goodForm.goodsIntro" placeholder="请输入商品介绍"></el-input>
        </el-form-item>
        <!-- <el-form-item label="商品价格" prop="originalPrice">
          <el-input type="number" min="0" style="width: 300px" v-model="state.goodForm.originalPrice" placeholder="请输入商品价格"></el-input>
        </el-form-item>
        <el-form-item label="商品售卖价" prop="sellingPrice">
          <el-input type="number" min="0" style="width: 300px" v-model="state.goodForm.sellingPrice" placeholder="请输入商品售价"></el-input>
        </el-form-item>
        <el-form-item label="商品库存" prop="stockNum">
          <el-input type="number" min="0" style="width: 300px" v-model="state.goodForm.stockNum" placeholder="请输入商品库存"></el-input>
        </el-form-item>
        <el-form-item label="商品标签" prop="tag">
          <el-input style="width: 300px" v-model="state.goodForm.tag" placeholder="请输入商品小标签"></el-input>
        </el-form-item>
        <el-form-item label="上架状态" prop="goodsSellStatus">
          <el-radio-group v-model="state.goodForm.goodsSellStatus">
            <el-radio label="0">上架</el-radio>
            <el-radio label="1">下架</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item required label="商品封面图" prop="goodsCoverImg"
        
        :rules="[{ 
    validator: (_, val, cb) => val.length > 0 ? cb() : cb(new Error('至少上传1张图片'))
  }]"
        >
          <el-upload
            class="avatar-uploader"
             accept="image/*"
            :action="state.uploadImgServer"
            :headers="{
              token: state.token
            }"
            :show-file-list="false"
            :before-upload="handleBeforeUpload"
            :on-change="handleUrlSuccess"
             :auto-upload="false"
          >
            <img style="width: 100px; height: 100px; border: 1px solid #e9e9e9;" v-if="state.goodForm.goodsCoverImg" :src="state.goodForm.goodsCoverImg" class="avatar">
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>

        <el-text 
  :truncated="true" 
  :line-clamp="2" 
  type="primary" 
  size="medium"
  style="margin-left: 30px;"
>
建议尺寸：800px*800px，上传小于500kb的图片
</el-text>

<!-- <el-form-item required label="商品轮播图" prop="goodsCoverImg">
          <el-upload
            class="avatar-uploader"
            :action="state.uploadImgServer"
            
      :multiple="true"
     
            accept="jpg,jpeg,png"
            :headers="{
              token: state.token
            }"
           
            :before-upload="handleBeforeUpload"
            :on-success="handleUrlSuccess"
            :on-remove="handleRemove"
          >
            <img style="width: 100px; height: 100px; border: 1px solid #e9e9e9;" v-if="state.goodForm.goodsCoverImg" :src="state.goodForm.goodsCoverImg" class="avatar">
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item> -->

        
  <el-form-item required label="商品轮播图" prop="goodsCarousel"
  :rules="[{ 
    validator: (_, val, cb) => val.length > 0 ? cb() : cb(new Error('至少上传1张图片'))
  }]"
  >
    <el-upload
    class="avatar-uploader"
    :show-file-list="false"
      :file-list="form.images"
      action="/api/upload"
      :multiple="true"
      :limit="10"
      :headers="{
              token: state.token
            }"
      accept="image/*"
      
      :auto-upload="false"
      :on-change="handleChangepic"
      :on-remove="handleRemove"
    >
      <el-icon  class="avatar-uploader-icon"><Plus /></el-icon>
      
    </el-upload>
    <div v-for="(url, index) in formurl" :key="index" class="preview-item">
    <img :src="url" class="preview-img" />
    <el-icon class="delete-icon" @click="handleRemove(index)">
      <Close />
    </el-icon>
  </div>
    <!-- 下方图片展示 -->
<!-- <div class="preview-container"> -->

<!-- </div> -->
  </el-form-item>




<el-text 
  :truncated="true" 
  :line-clamp="2" 
  type="primary" 
  size="medium"
  style="margin-left: 30px;"
>
建议尺寸：800px*800px，上传小于500kb的图片，最多可上传10张图片
</el-text>
        <!-- <el-form-item label="详情内容">
          <div ref='editor'></div>
        </el-form-item> -->
        <el-form-item>
          <!-- <el-button type="primary" @click="submitAdd()">{{ state.id ? '立即修改' : '立即创建' }}</el-button> -->
          <el-button type="primary" @click="go()">下一步</el-button>

        </el-form-item>
      </el-form>
    </el-card>


    <el-card v-show="activeName === '2'" class="add-container">

      <el-form :model="state.goodForm" :rules="state.rules" ref="goodRef" label-width="100px" class="goodForm">
      <el-form-item label="商品规格" prop="goodsspecs">
          <el-radio-group v-model="state.goodForm.goodsspecs">
            <el-radio label="0">单规格</el-radio>
            <!-- <el-radio label="1">下架</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-text 
  :truncated="true" 
  :line-clamp="2" 
  type="primary" 
  size="medium"
  style="margin-left: 30px;"
>
价格设置范围 0.01~999999.99
</el-text>
<el-table
    :data="tableData"
    style="width: 60%;margin-left: 30px;"
    border
    highlight-current-row
  >
    <!-- 图片列（按钮） -->
    <el-table-column label="图片" width="120"
    :rules="[{ 
    validator: (_, val, cb) => val.length > 0 ? cb() : cb(new Error('至少上传1张图片'))
  }]"
    >
      <template #default="{ $index }">
        <el-upload
            class="avatar-uploader"
            :action="state.uploadImgServer"
            accept="image/*"
            :headers="{
              token: state.token
            }"
            :show-file-list="false"
            :before-upload="handleBeforeUpload"
            :on-change="handleUrlSuccessSku"
            
          >
            <img style="width: 100px; height: 100px; border: 1px solid #e9e9e9;" v-if="state.goodForm.goodsSkuImg" :src="state.goodForm.goodsSkuImg" class="avatar">
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
      </template>
    </el-table-column>

    <!-- 售价列（数字编辑） -->
    <el-table-column label="售价（元）" prop="goodsSellingPrice" width="150">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsSellingPrice'"
          v-model.number="row.goodsSellingPrice"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsSellingPrice')">
          {{ row.goodsSellingPrice || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 成本价列（数字编辑） -->
    <el-table-column label="成本价（元）" prop="goodsCostPrice" width="150">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsCostPrice'"
          v-model.number="row.goodsCostPrice"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsCostPrice')">
          {{ row.goodsCostPrice || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 原价列（数字编辑） -->
    <el-table-column label="原价（元）" prop="goodsOriginalPrice" width="150">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsOriginalPrice'"
          v-model.number="row.goodsOriginalPrice"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsOriginalPrice')">
          {{ row.goodsOriginalPrice || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 库存列（数字编辑） -->
    <el-table-column label="库存" prop="goodsStockNum" width="120">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsStockNum'"
          v-model.number="row.goodsStockNum"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsStockNum')">
          {{ row.goodsStockNum || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 库存预警列（开关编辑） -->
    <el-table-column label="库存预警" prop="goodsStockWarn" width="120">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsStockWarn'"
          v-model.number="row.goodsStockWarn"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsStockWarn')">
          {{ row.goodsStockWarn || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 自定义规格列（文本编辑） -->
    <el-table-column v-if="categoryTypex===0"  prop="goodsSpecification">
      <template #header="{ column }">
    <!-- 可编辑表头区域 -->
    <el-input
      v-if="headerEdit.active"
      v-model="headerLabel"
      size="small"
      @blur="saveHeaderEdit"
    />
    <span v-else @click="startHeaderEdit">
      {{ headerLabel || '点击编辑表头' }}
    </span>
  </template>
      <template #default="{ row, $index }">
        <el-input
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsSpe'"
          v-model="row.goodsSpe"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsSpe')">
          {{ row.goodsSpe || '点击编辑规格' }}
        </span>
      </template>
    </el-table-column>
    <el-table-column v-if="categoryTypex===1"  label="重量(kg)" prop="goodsWeight" width="120">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsWeight'"
          v-model.number="row.goodsWeight"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsWeight')">
          {{ row.goodsWeight || 0 }}
        </span>
      </template>
    </el-table-column>
    <el-table-column v-if="categoryTypex===1"  label="体积(平方)" prop="goodsVolume" width="120">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsVolume'"
          v-model.number="row.goodsVolume"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsVolume')">
          {{ row.goodsVolume || 0 }}
        </span>
      </template>
    </el-table-column>
  </el-table>
<el-form-item label="商品销量" prop="goodsVirtualSellCount">
          <el-input style="width: 400px" v-model="state.goodForm.goodsVirtualSellCount" placeholder="
  可设置虚拟销量，总销量-虚拟销量=实际销量"></el-input>
        </el-form-item>

        <el-form-item label="详情内容" prop="goodsDetailContent">
          <div ref='editor'></div>
        </el-form-item>
        <el-form-item label="是否核销" prop="goodsType">
          <el-radio-group v-model="state.goodForm.goodsType">
            <el-radio label="0">否</el-radio>
            <el-radio label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否上架" prop="goodsSellStatus">
          <el-radio-group v-model="state.goodForm.goodsSellStatus">
            <el-radio label="2">否</el-radio>
            <el-radio label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <!-- <el-button type="primary" @click="submitAdd()">{{ state.id ? '立即修改' : '立即创建' }}</el-button> -->
          <el-button type="primary" @click="back()">上一步</el-button>
          <el-button v-if="option!=='preview'" type="primary" @click="submitAdd()">确定</el-button>
        </el-form-item>
      </el-form>

    </el-card>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, onBeforeUnmount, getCurrentInstance,watch } from 'vue'
import WangEditor from 'wangeditor'
import axios from '@/utils/axios'
import { ElMessage } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { localGet, uploadImgServer, uploadImgsServer } from '@/utils'

const { proxy } = getCurrentInstance()
const editor = ref(null)
const goodRef = ref(null)
const route = useRoute()
const router = useRouter()
const { id,option } = route.query
const selectedId = ref([0]); // 默认选中「自营」
const activeName = ref('1');

// 获取组件实例
const cascaderRef = ref();

const categoryTypex = ref(0)
const categoryName =ref('')


const options = [
  { value: 1, label: '自营' },
  { value: 2, label: '第三方' }
];

const optionList = ref([])
const selectedValue = ref('')

const optionsmerchants = ref([]);
const selectedValuemerchants = ref('');

const selectedValuecategory = ref([]);


const optionsorgans = ref([]);
const selectedValueorgans = ref('');

const headerEdit = reactive({
  active: false,
});
const headerLabel = ref('自定义规格'); // 初始表头文本

const startHeaderEdit = () => {
  headerEdit.active = true;
};

const saveHeaderEdit = () => {
  headerEdit.active = false;
  // 可在此处添加保存到后端的逻辑:ml-citation{ref="6,7" data="citationList"}
};


// 表格数据
const tableData = ref([
  {
    imgUrl: '',
    price: 99.99,
    costPrice: 50.00,
    originalPrice: 129.99,
    stock: 100,
    stockAlert: true,
    specs: '标准版'
  }
]);

// 当前编辑状态
const activeEdit = reactive({
  index: -1,
  field: ''
});

// 开始编辑
const startEdit = (index, field) => {
  activeEdit.index = index;
  activeEdit.field = field;
};

// 保存编辑
const saveEdit = (index) => {
  activeEdit.index = -1;
  activeEdit.field = '';
  // 这里可以添加数据保存逻辑
  // ElMessage.success(`第 ${index + 1} 行数据已更新`);
};

// 库存预警状态变更
const handleStockAlertChange = (index) => {
  ElMessage.info(`库存预警状态已更新: ${tableData.value[index].stockAlert ? '开启' : '关闭'}`);
};

// 图片上传处理
const handleImageUpload = (index) => {
  // 这里添加实际图片上传逻辑
  ElMessage.info(`开始上传第 ${index + 1} 行的图片`);
};

const handleTabClick = (tab) => {
  // console.log('当前激活标签:', tab.props.name);
  // if (tab.props.name === '1') {
  //   console.log('当前激活标签:', tab.props.name);
  //   activeName = '1';
  // }else{
  //   console.log('当前激活标签:', tab.props.name);
  //   activeName = '2';
  // }
  // this.$refs.tabs.setCurrentName("1"); // 参数为要切换的tab的name

  activeName.value = tab.props.name;
  console.log('当前激活标签:', activeName);
};

const form = reactive({
  images: [] // 存储原始文件对象
});


const formurl = ref([]); 

const previewList = ref([]); // 存储预览图URL

// 生成Base64预览
const generatePreview = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.readAsDataURL(file);
  });
};

const handleChangepic = async (file) => {
  // 数量校验
  if (form.images.length >= 10) {
    ElMessage.warning('最多选择10张图片');
    return false;
  }

  // 格式校验
  if (!['image/jpeg', 'image/png'].includes(file.raw?.type || '')) {
    ElMessage.error('仅支持JPG/PNG格式');
    return false;
  }

  // if (!['jpg', 'jpeg', 'png'].includes(file.raw?.type || '')) {
  //   ElMessage.error('请上传 jpg、jpeg、png 格式的图片')
  //   return false
  // }
  // console.log('previewUrl')
  // 生成预览并更新数据
  try {
    const previewUrl = await generatePreview(file.raw);
    // console.log(previewUrl,'previewUrl')
    previewList.value.push(previewUrl);
    form.images.push(file.raw);

    const formData = new FormData();
    formData.append('file', file.raw); // 文件字段
  // form.images.forEach((file, index) => {
  //   formData.append(`images[${index}]`, file);
  // });
  

  axios.post('/upload/file', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }).then(res => {
    // 处理成功逻辑
    // console.log(res, 'res');
    // if(res.resultCode === 200){
      formurl.value.push(res.url);
      // state.ruleForm.merchantLogo = res.url
    // }
  });



  } catch (error) {
    console.error('生成预览失败:', error);
    ElMessage.error('文件读取失败');
  }
};

const handleRemove = (index) => {
  console.log(index,'index')
  previewList.value.splice(index, 1);
  form.images.splice(index, 1);
  formurl.value.splice(index, 1);
  // console.log(previewList.value,'index')
  // console.log(form.images,'index')
  console.log(formurl.value,'index')
};

// const form = reactive({
//   images: [] as File[] // 存储原始文件对象
// });

// const previewList = ref<string[]>([]); // 存储预览图URL

// // 生成Base64预览
// const generatePreview = (file: File) => {
//   return new Promise<string>((resolve) => {
//     const reader = new FileReader();
//     reader.onload = (e) => resolve(e.target?.result as string);
//     reader.readAsDataURL(file);
//   });
// };

// const handleChange = async (file: UploadFile) => {
//   // 数量校验
//   if (form.images.length >= 10) {
//     ElMessage.warning('最多选择10张图片');
//     return false;
//   }

//   // 格式校验
//   if (!['image/jpeg', 'image/png'].includes(file.raw?.type || '')) {
//     ElMessage.error('仅支持JPG/PNG格式');
//     return false;
//   }

//   // 生成预览并更新数据
//   const previewUrl = await generatePreview(file.raw);
//   previewList.value.push(previewUrl);
//   form.images.push(file.raw);
// };

// const handleRemove = (index: number) => {
//   previewList.value.splice(index, 1);
//   form.images.splice(index, 1);
// };

const submitForm = () => {
  const formData = new FormData();
  form.images.forEach((file, index) => {
    formData.append(`images[${index}]`, file);
  });

  axios.post('/api/submit', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }).then(res => {
    // 处理成功逻辑
  });
};


const state = reactive({
  uploadImgServer,
  token: localGet('token') || '',
  id: id,
  defaultCate: '请选择商品分类',
  goodForm: {
    goodsName: '',
    goodsSubName: '',
    goodsIntro: '',
    originalPrice: '',
    sellingPrice: '',
    stockNum: '',
    goodsSellStatus: '2',
    goodsCoverImg: '',
    goodsSkuImg: '',
    goodsspecs:'0',
    goodsVirtualSellCount: '',
    
    goodsType:'',
    tag: ''
  },
  rules: {
    goodsName: [
      { required: 'true', message: '请填写商品名称', trigger: ['change'] }
    ],
    originalPrice: [
      { required: 'true', message: '请填写商品价格', trigger: ['change'] }
    ],
    sellingPrice: [
      { required: 'true', message: '请填写商品售价', trigger: ['change'] }
    ],
    stockNum: [
      { required: 'true', message: '请填写商品库存', trigger: ['change'] }
    ],
    defaultCate: [
    { type: 'array', required: true, message: '请选择商品分类', trigger: 'change' }
  ],
 
   // 修改商品分类验证规则
   categoryId: [
      { 
        required: true, 
        message: '请选择商品分类', 
        trigger: 'change',
        validator: (rule, value, callback) => {
          if (!state.categoryId) {
            callback(new Error('请选择商品分类'))
          } else {
            callback()
          }
        }
      }
    ],
    
    // 修改所属类型验证规则
    selectedId: [
      { 
        required: true, 
        message: '请选择所属类型', 
        trigger: 'change',
        validator: (rule, value, callback) => {
          if (!selectedId.value) {
            callback(new Error('请选择所属类型'))
          } else {
            callback()
          }
        }
      }
    ],
    
    // 修改所属商户验证规则
    goodsBelongMerchantId: [
      { 
        required: true, 
        message: '请选择所属商户', 
        trigger: 'change',
        validator: (rule, value, callback) => {
          if (!selectedValuemerchants.value) {
            callback(new Error('请选择所属商户'))
          } else {
            callback()
          }
        }
      }
    ],
  // defaultCates: [
  //   { type: 'array', required: true, message: '请选择所属类型', trigger: 'change' }
  // ],
  // selectedId: [
  //   {  required: true, 
  //     message: '请选择商品归属', 
  //     trigger: ['change', 'blur'],   }
  // ],
  selectedValue: [
    { type: 'array', required: true, message: '请选择所属机构', trigger: 'change' }
  ],
  },
  
  categoryId: '',
  category: {
    lazy: true,
    lazyLoad(node, resolve) {
      const { level = 0, value } = node
      if (level === 0) {
        axios.get('/categories', {
        params: {
          pageNumber: 1,
          pageSize: 1000,
          
        }
      }).then(res => {
        const list = res.list
        const nodes = list.map(item => ({
          value: item.categoryId,
          label: item.categoryName,
          categoryType:item.categoryType,
          // leaf: level > 1
          leaf: item.childrenList===null
        }))
        resolve(nodes)
      })
      } else if (level === 1) {
        axios.get('/categoryList', {
        params: {
          pageNumber: 1,
          pageSize: 1000,
          categoryLevel: level + 1,
          parentId: value || 0
        }
      }).then(res => {
        const list = res.list
        const nodes = list.map(item => ({
          value: item.categoryId,
          label: item.categoryName,
          categoryType:item.categoryType,
          // leaf: level > 1
          leaf: true
        }))
        resolve(nodes)
      })
      }
     
    }
  }
})
let instance
onMounted(() => {
  console.log('option', option)
  instance = new WangEditor(editor.value)
  instance.config.showLinkImg = false
  instance.config.showLinkImgAlt = false
  instance.config.showLinkImgHref = false
  instance.config.uploadImgMaxSize = 2 * 1024 * 1024 // 2M
  instance.config.uploadFileName = 'file'
  instance.config.uploadImgHeaders = {
    token: state.token
  }
  // 图片返回格式不同，需要自定义返回格式
  instance.config.uploadImgHooks = {
    // 图片上传并返回了结果，想要自己把图片插入到编辑器中
    // 例如服务器端返回的不是 { errno: 0, data: [...] } 这种格式，可使用 customInsert
    customInsert: function(insertImgFn, result) {
      console.log('result', result)
      // result 即服务端返回的接口
      // insertImgFn 可把图片插入到编辑器，传入图片 src ，执行函数即可
      if (result.data && result.data.length) {
        result.data.forEach(item => insertImgFn(item))
      }
    }
  }
  instance.config.uploadImgServer = uploadImgsServer
  Object.assign(instance.config, {
    onchange() {
      console.log('change')
    },
  })
  instance.create()
  if (id) {
    axios.get(`/goods/${id}`).then(res => {
      const { goods, firstCategory, secondCategory, thirdCategory } = res
      state.goodForm = {
        goodsName: goods.goodsName,
        goodsIntro: goods.goodsIntro,
        goodsType:String(goods.goodsType),
        goodsVirtualSellCount:goods.goodsVirtualSellCount,
        goodsSubName:goods.goodsSubName,
        goodsSellStatus: String(goods.goodsSellStatus),
        goodsspecs:'0',
        goodsCoverImg: proxy.$filters.prefix(goods.goodsCoverImg),
       
      }
      tableData.value[0].goodsOriginalPrice = goods.goodsOriginalPrice
      tableData.value[0].goodsSellingPrice = goods.goodsSellingPrice
      tableData.value[0].goodsStockNum = goods.goodsStockNum
      tableData.value[0].goodsCostPrice = goods.goodsCostPrice
      tableData.value[0].goodsStockWarn = goods.goodsStockWarn

      formurl.value = goods.goodsCarousel.split(',').map(url => url.trim());
      // console.log( formurl.value,'formurl.value')
      // previewList.value.push(goodsCarousel);
      selectedValuemerchants.value =goods.goodsBelongMerchantId
      selectedValueorgans.value=goods.goodsBelongOrganId
     
      
      tableData.value[0].goodsWeight = goods.goodsWeight
      tableData.value[0].goodsVolume = goods.goodsVolume
      
      const goodsSpecificationObject = JSON.parse(goods.goodsSpecification);

      state.goodForm.goodsSkuImg = goodsSpecificationObject.sku;
      const parts = String(goodsSpecificationObject.spec).split(':');
      headerLabel.value=  parts[0]?.trim() || ''
      tableData.value[0].goodsSpe=parts[1]?.trim() || ''
      console.log( goodsSpecificationObject.spec,'111111')
      console.log( parts[1]?.trim() || '','222222')
      if(option !== 'copy'){
        state.categoryId = goods.goodsCategoryId
      state.defaultCate = goods.goodsCategoryName
      selectedValuecategory.value = goods.goodsCategoryId
      selectedId.value = goods.goodsBelongType
      }
     
      // state.goodForm.goodsspecs 
      // state.defaultCate = `${firstCategory.categoryName}/${secondCategory.categoryName}/${thirdCategory.categoryName}`
      if (instance) {
        // 初始化商品详情 html
        instance.txt.html(goods.goodsDetailContent)
      }
    })
  }
  getOrderList()
  getOrgans()
})

const getOrgans = () => {
  // state.loading = true

  // console.log(selectedstateId ,'index')
  axios.get('/organs', {
    params: {
      pageNumber: 1,
      pageSize: 100,
      
    }
  }).then(res => {
    optionsorgans.value = res.list

  })
}

const getOrderList = () => {
  // state.loading = true

  // console.log(selectedstateId ,'index')
  axios.get('/merchants', {
    params: {
      pageNumber: 1,
      pageSize: 100,
      merchantName: '',
      status: ''
    }
  }).then(res => {
    optionsmerchants.value = res.list

  })
}

onBeforeUnmount(() => {
  instance.destroy()
  instance = null
})
const go = () => {
  activeName.value = '2';
}
const back = () => {
  activeName.value = '1';
}




const submitAdd = () => {
  goodRef.value.validate((vaild) => {
    if (vaild) {
      const myMap = new Map([
  ['sku', state.goodForm.goodsSkuImg],
  ['spec',headerLabel.value+ ': ' +tableData.value[0].goodsSpe]
]);
console.log('headerLabel', headerLabel)
console.log('tableData.value[0].goodsSpe', tableData.value[0].goodsSpe)
// Map 转普通对象
const mapToObject = Object.fromEntries(myMap.entries());

// 转 JSON 字符串
const jsonStr = JSON.stringify(mapToObject);
// 结果: {"name":"John","123":{"age":30}}
      // 默认新增用 post 方法
      let httpOption = axios.post
      let params = {
        goodsCategoryId: state.categoryId,
        goodsCoverImg: state.goodForm.goodsCoverImg,
        goodsCarousels:formurl.value,
        goodsDetailContent: instance.txt.html(),
        goodsIntro: state.goodForm.goodsIntro,
        goodsName: state.goodForm.goodsName,
        goodsSubName:state.goodForm.goodsSubName,
        goodsBelongType:selectedId.value=== undefined?'':selectedId.value.length?'':selectedId.value,
        goodsBelongMerchantId:selectedValuemerchants.value,
        goodsBelongOrganId:selectedValueorgans.value,
        goodsSellStatus: +state.goodForm.goodsSellStatus,
        goodsOriginalPrice: tableData.value[0].goodsOriginalPrice,
        goodsSellingPrice: tableData.value[0].goodsSellingPrice,
        goodsCostPrice:tableData.value[0].goodsCostPrice,
        goodsStockNum: tableData.value[0].goodsStockNum,
        goodsStockWarn:tableData.value[0].goodsStockWarn,
        goodsWeight:tableData.value[0].goodsWeight,
        goodsVolume:tableData.value[0].goodsVolume,
        goodsSpecification:jsonStr,
        goodsType:state.goodForm.goodsType,
        goodsVirtualSellCount:state.goodForm.goodsVirtualSellCount,
        goodsCategoryName:categoryName.value===''?state.defaultCate:categoryName.value,
        goodsNeedWriteOff:+state.goodForm.goodsType
      }
      console.log('params', params)
      if(option === 'edit'){
        if (id) {
        params.goodsId = id
        // 修改商品使用 put 方法
        httpOption = axios.put
      }
      }
    
      httpOption('/goods', params).then(() => {
        ElMessage.success(id ? '修改成功' : '添加成功')
        router.push({ path: '/good' })
      })
    }
  })
}
const handleBeforeUpload = (file) => {
  const sufix = file.name.split('.')[1] || ''
  if (!['jpg', 'jpeg', 'png'].includes(sufix)) {
    ElMessage.error('请上传 jpg、jpeg、png 格式的图片')
    return false
  }
}
const handleUrlSuccess = async (file) => {
  try {


    console.log('当前选中值:', 2222222222222) 
    const previewUrl = await generatePreview(file.raw);
    state.goodForm.goodsCoverImg = previewUrl || ''
    // console.log(previewUrl,'previewUrl')
    // previewList.value.push(previewUrl);
    // form.images.push(file.raw);
    const formData = new FormData();
    formData.append('file', file.raw); // 文件字段
  // form.images.forEach((file, index) => {
  //   formData.append(`images[${index}]`, file);
  // });
  

  axios.post('/upload/file', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }).then(res => {
    // 处理成功逻辑
    // console.log(res, 'res');
    // if(res.resultCode === 200){
      state.goodForm.goodsCoverImg = res.url
    // }
  });
  } catch (error) {
    console.error('生成预览失败:', error);
    ElMessage.error('文件读取失败');
  }
  // state.goodForm.goodsCoverImg = val.data || ''
  // console.log('当前选中值:', val) 
}


const handleUrlSuccessSku = async (file) => {
  try {
    const previewUrl = await generatePreview(file.raw);
    state.goodForm.goodsSkuImg = previewUrl || ''
    // console.log(previewUrl,'previewUrl')
    // previewList.value.push(previewUrl);
    // form.images.push(file.raw);
    const formData = new FormData();
    formData.append('file', file.raw); // 文件字段
  // form.images.forEach((file, index) => {
  //   formData.append(`images[${index}]`, file);
  // });
  

  axios.post('/upload/file', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }).then(res => {
    // 处理成功逻辑
    // console.log(res, 'res');
    // if(res.resultCode === 200){
      state.goodForm.goodsSkuImg = res.url
    // }
  });
  } catch (error) {
    console.error('生成预览失败:', error);
    ElMessage.error('文件读取失败');
  }
  // state.goodForm.goodsCoverImg = val.data || ''
  // console.log('当前选中值:', val) 
}

const handleChangeCate = (val) => {
  console.log(val, 'state.categoryId') 
  if(val!==undefined  ){
    if(val.length>1){
 state.categoryId = val[1] || 0
  }else{
    state.categoryId = val[0]|| 0
  }
  // state.categoryId = val[0]||val[1] || 0
  console.log(state.categoryId, 'state.categoryId') 
  }else{
    state.categoryId = ''
  }


// 获取选中节点的完整数据
const nodes = cascaderRef.value.getCheckedNodes();
  
  if (nodes.length > 0) {
    const currentData = nodes[0].data; // 当前节点数据
    const pathData = nodes[0].pathNodes; // 路径所有节点
    
    // // 示例获取属性
    console.log('当前节点ID:', currentData.value);
    console.log('分类名称:', currentData.label);
    console.log('所属层级:', currentData.categoryType);
    console.log('父级ID:', pathData[pathData.length-2]?.value);
    categoryName.value =  currentData.label
    categoryTypex.value = currentData.categoryType
    console.log('所属层级ss:', categoryTypex.value);

   
  }
 // 手动触发表单验证
 goodRef.value?.validateField('categoryId')

}

// 监听所属类型变化
watch(selectedId, (newVal) => {
  if (newVal) {
    // 手动触发表单验证
    goodRef.value?.validateField('selectedId')
  }
})

// 监听所属商户变化
watch(selectedValuemerchants, (newVal) => {
  if (newVal) {
    // 手动触发表单验证
    goodRef.value?.validateField('goodsBelongMerchantId')
  }
})

const handleChange = (val) => {
  console.log('当前选中值:', val) // 输出 value 值（如 1001）
}

</script>

<style scoped>

.el-table {
  margin: 20px 0;
}

.el-input-number,
.el-input {
  width: 100%;
}

.el-switch {
  margin-left: 8px;
}

span {
  cursor: pointer;
  user-select: none;
}

span:hover {
  color: #409eff;
}
  .add {
    /* display: flex; */
  }
  .add-container {
    flex: 1;
   margin-top: -30px;
  overflow-y: auto;
  padding: 16px;
  }
 
  .avatar-uploader {
    width: 100px;
    height: 100px;
    color: #ddd;
    font-size: 30px;
  }
  .avatar-uploader-icon {
    display: block;
    width: 100%;
    height: 100%;
    border: 1px solid #e9e9e9;
    padding: 32px 17px;
  }
  .preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 20px;
}

.preview-item {
  position: relative;
  width: 100px;
  height: 100px;
  padding: 10px;
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  
}

.delete-icon {
  position: absolute;
  
  right: 8px;
  cursor: pointer;
  background: #ff4d4f;
  color: white;
  border-radius: 50%;
  padding: 3px;
}
</style>
<style>
 .custom-menu .el-cascader-menu {
  max-height: 80px;
}

.el-tabs {
  border: none !important; /* 清除外层容器边框:ml-citation{ref="3" data="citationList"} */
}

.el-tabs__content {
  border: none !important; /* 去除内容区域边框:ml-citation{ref="3" data="citationList"} */
}

/* 固定定位的 Tab 栏 */
.fixed-tabs {
  /* position: sticky;
  top: 0;
  left: 0; */
  width: 100%;
  z-index: 1000;
  background: #fff;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1); /* 增加层次感:ml-citation{ref="1" data="citationList"} */
}
</style>