<template>
  <el-card class="good-container">
    <template #header>
      <div class="header">
        <el-input
          style="width: 200px; margin-right: 10px"
          placeholder="请输入商品名称"
          v-model="state.searchName"
          clearable
        />
        <!-- <el-select clearable filterable 
        reserve-keyword placeholder="请选择商品分类" 
          v-model="selectedValuecategories"
    :loading="loadingcategories"
        style="width: 200px; margin-right: 10px">
          <el-option
            v-for="item in optionscategories"
            :key="item.categoryId"
            :label="item.categoryName"
            :value="item.categoryId"
          />
        </el-select>
        <el-select clearable filterable 
        reserve-keyword  placeholder="请选择商品归属"  v-model="selectedId" :options="options" style="width: 200px; margin-right: 10px">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select> -->
        <el-select clearable filterable 
        reserve-keyword  placeholder="请选择商户" 
        v-model="selectedValuemerchants" style="width: 200px; margin-right: 10px">
          <el-option
            v-for="item in optionsmerchants"
            :key="item.merchantId"
            :label="item.merchantName"
            :value="item.merchantId"
          />
        </el-select>
        <el-select 
          filterable 
          reserve-keyword 
          clearable 
          placeholder="请选择供应商" 
          v-model="selectedSupplierId" 
          style="width: 200px; margin-right: 10px"
        >
          <el-option
            v-for="item in supplierOptions"
            :key="item.supplierId"
            :label="item.supplierName"
            :value="item.supplierId"
          />
        </el-select>
        <el-select filterable 
        reserve-keyword clearable  placeholder="请选择状态" v-model="selectedstateId" :options="optionsstate" style="width: 200px; margin-right: 10px">
          <el-option
            v-for="item in optionsstate"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-select 
          filterable 
          reserve-keyword 
          clearable 
          placeholder="请选择审核状态" 
          v-model="selectedAuditStatusId" 
          style="width: 200px; margin-right: 10px"
        >
          <el-option
            v-for="item in auditStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-button type="primary" :icon="HomeFilled" @click="handleOption">搜索</el-button>
        <el-button type="primary" :icon="HomeFilled" @click="handleOption('1')">重置</el-button>
        <el-button type="primary" :icon="Plus" @click="handleAdd">添加商品</el-button>
      </div>
    </template>
    <el-table
      :load="state.loading"
      :data="state.tableData"
      tooltip-effect="dark"
      style="width: 100%"
    >
      <el-table-column
      align="center"
        prop="goodsId"
        label="商品编号"
        width="100px"
      >
      </el-table-column>
      
      <!-- <el-table-column
        prop="goodsIntro"
        label="商品简介"
      >
      </el-table-column> -->
      <el-table-column
      align="center"
        label="商品图片"
        width="150px"
      >
        <template #default="scope">
          <img style="width: 100px; height: 100px;" :key="scope.row.goodsId" :src="$filters.prefix(scope.row.goodsCoverImg)" alt="商品主图">
        </template>
      </el-table-column>
      <el-table-column
      align="center"
        prop="goodsName"
        label="商品名称"
        width="100px"
      >
      </el-table-column>      
      <el-table-column
        align="center"
        prop="goodsMerchantName"
        label="商户名称"
        width="100px"
      />
      
      <el-table-column
        align="center"
        prop="goodsSupplierName"
        label="供应商名称"
        width="100px"
      />
      <el-table-column
      align="center"
        prop="goodsSellingPrice"
        label="商品售价"
        width="100px"
      >
      </el-table-column>
      <el-table-column
      align="center"
        prop="goodsSellCount"
        label="商品销量"
        width="100px"
      >
      </el-table-column>
      <el-table-column
      align="center"
        prop="goodsStockNum"
        label="商品库存"
        width="100px"
      >
      </el-table-column>
      
      <!-- <el-table-column
        label="上架状态"
      >
        <template #default="scope">
          <span style="color: green;" v-if="scope.row.goodsSellStatus == 0">销售中</span>
          <span style="color: red;" v-else>已下架</span>
        </template>
      </el-table-column> -->
      <el-table-column label="状态" align="center" prop="goodsSellStatus" width="120">
      <template #default="{ row }">
        <span v-if="row.goodsSellStatus === 1">上架</span>
        <span v-else-if="row.goodsSellStatus === 2">下架</span>
        <span v-else-if="row.goodsSellStatus === 3">库存预警</span>
        <span v-else-if="row.goodsSellStatus === 4">售罄</span>
        <span v-else-if="row.goodsSellStatus === 5">审核中</span>
      </template>
      </el-table-column>

          <el-table-column 
        label="审核状态" 
        align="center" 
        width="100"
      >
        <template #default="{ row }">
          <span v-if="row.auditStatus === 0">待审核</span>
          <span v-else-if="row.auditStatus === 1">审核通过</span>
          <span v-else-if="row.auditStatus === 2">审核不通过</span>
        </template>
      </el-table-column>

      <el-table-column
      align="center"
        label="操作"
        width="250"
      >
      <!-- 提交审核 审核   删除  -->
        <template #default="scope">
          <a style="cursor: pointer; margin-right: 10px" v-if="[1, 3, 4].includes(scope.row.goodsSellStatus)" @click="handleEdit(scope.row.goodsId,'preview')">预览</a>
          <a style="cursor: pointer; margin-right: 10px" v-if="[2,0].includes(scope.row.goodsSellStatus)" @click="handleSubmitAudit(scope.row)">提交审核</a>
          <a style="cursor: pointer; margin-right: 10px" v-if="[5].includes(scope.row.goodsSellStatus)" @click="handleAudit(scope.row)">审核</a>
          <a style="cursor: pointer; margin-right: 10px" v-if="[1, 2, 3, 4,0].includes(scope.row.goodsSellStatus)" @click="handleEdit(scope.row.goodsId,'edit')">编辑</a>
          <a style="cursor: pointer; margin-right: 10px" v-if="[1, 3, 4].includes(scope.row.goodsSellStatus)" @click="handleEditStock(scope.row)">编辑库存</a>
          <a style="cursor: pointer; margin-right: 10px" v-if="[1, 3, 4].includes(scope.row.goodsSellStatus)" @click="handleTakeDown(scope.row.goodsId, 2)">下架</a>
          <a style="cursor: pointer; margin-right: 10px" @click="handleEdit(scope.row.goodsId,'copy')">复制</a>
          <a style="cursor: pointer; margin-right: 10px" v-if="[2,0].includes(scope.row.goodsSellStatus)" @click="handleDelete(scope.row.goodsId)">删除</a>
          <!-- <a style="cursor: pointer; margin-right: 10px" v-else @click="handleStatus(scope.row.goodsId, 0)">上架</a> -->
        </template>
      </el-table-column>
    </el-table>
    <!--总数超过一页，再展示分页器-->
    <el-pagination
      background
      layout="prev, pager, next"
      :total="state.total"
      :page-size="state.pageSize"
      :current-page="state.currentPage"
      @current-change="changePage"
    />
  </el-card>
  <DialogAddSwiper ref='addSwiper' :reload="getGoodList" :type="state.type" />
  <DialogAudit ref="auditDialogRef" @reload="getGoodList" />
</template>

<script setup>
import { onMounted, reactive, getCurrentInstance,ref, toRefs } from 'vue'
import axios from '@/utils/axios'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import DialogAddSwiper from '@/components/DialogEditStock.vue'
import DialogAudit from '@/components/DialogAudit.vue'

const addSwiper = ref()
const auditDialogRef = ref()
const app = getCurrentInstance()
const { goTop } = app.appContext.config.globalProperties
const router = useRouter()
const state = reactive({
  loading: false,
  tableData: [], // 数据列表
  total: 0, // 总条数
  currentPage: 1, // 当前页
  pageSize: 10, // 分页大小
  type: 'add',
  orderStatus:0,
  tableDatacategories: [],
})
const selectedId = ref([0]); // 默认选中「自营」


const optionscategories = ref([]);
const selectedValuecategories = ref('');
const loadingcategories = ref(false);


const optionsmerchants = ref([]);
const selectedValuemerchants = ref('');

const options = [
  { value: 1, label: '自营' },
  { value: 2, label: '商户' }
];

const selectedstateId = ref([0]); // 默认选中「自营」

const optionsstate = [
  { value: 1, label: '上架' },
  { value: 2, label: '下架' },
  { value: 3, label: '库存预警' },
  { value: 4, label: '售罄' },
  { value: 5, label: '审核中' },
];

// 审核状态选项
const auditStatusOptions = [
  { value: 0, label: '待审核' },
  { value: 1, label: '审核通过' },
  { value: 2, label: '审核不通过' }
]
// 在其他ref变量定义的地方添加以下代码（约在第240-260行之间）
const selectedSupplierId = ref('');
const supplierOptions = ref([]);

// 在onMounted钩子中添加getSupplierList调用（约在第280行附近）
onMounted(() => {
  getGoodList()
  getcategories()
  getOrderList()
  getSupplierList() // 添加这一行
})

// 在其他获取列表方法后添加以下方法（约在第450行之后）
// 获取供应商列表
const getSupplierList = () => {
  axios.get('/supplier/list', {
    params: {
      supplierName: '',
      status: 1,
      pageNumber: 1,
      pageSize: 100
    }
  }).then(res => {
    supplierOptions.value = res.list
  }).catch(err => {
    console.error('获取供应商列表失败:', err)
  })
}

// 在handleOption方法中添加重置供应商选择（约在第370行附近）
const handleOption = (index) => {
  // console.log(index,'index')
  if(index === '1'){
    selectedId.value = ''
    selectedstateId.value = ''
    selectedValuecategories.value = ''
    selectedValuemerchants.value = ''
    selectedSupplierId.value = '' // 添加这一行
  }
  state.currentPage = 1
  getGoodList()
}

// 在getGoodList方法的params中添加供应商ID参数（约在第430行附近）
axios.get('/goods/list', {
  params: {
    pageNumber: state.currentPage,
    pageSize: state.pageSize,
    goodsBelongType:selectedId.value=== undefined?'':selectedId.value.length?'':selectedId.value,
    goodsSellStatus:selectedstateId.value=== undefined?'':selectedstateId.value.length?'':selectedstateId.value,
    goodscategoryId:selectedValuecategories.value=== undefined?'':selectedValuecategories.value.length?'':selectedValuecategories.value, 
    goodsBelongMerchantId:selectedValuemerchants.value=== undefined?'':selectedValuemerchants.value.length?'':selectedValuemerchants.value,
    goodsName:state.searchName,
    goodsServiceFlag:0,
    supplierId:selectedSupplierId.value=== undefined?'':selectedSupplierId.value.length?'':selectedSupplierId.value, // 添加这一行
  }
})
const handleStockAlertChange = (index) => {
  ElMessage.info(`状态已更新: ${state.tableData[index].goodsSellStatus ? '开启' : '关闭'}`);
};

// 审核商品
const handleAudit = (row) => {
  auditDialogRef.value.open(row)
}

// 提交审核
const handleSubmitAudit = (row) => {
  if (!row || !row.goodsId) {
    ElMessage.error('商品ID不能为空')
    return
  }

  axios.put('/goods', { 
    goodsId: row.goodsId, 
    goodsName: row.goodsName, 
    goodsSubName: row.goodsSubName, 
    goodsCategoryId: row.goodsCategoryId, 
    goodsCategoryName: row.goodsCategoryName, 
    goodsBelongType: row.goodsBelongType, 
    goodsBelongMerchantId: row.goodsBelongMerchantId, 
    goodsBelongOrganId: row.goodsBelongOrganId, 
    goodsIntro: row.goodsIntro, 
    goodsCoverImg: row.goodsCoverImg, 
    goodsCarousels: row.goodsCarousel, 
    goodsSpecification: row.goodsSpecification, 
    goodsVirtualSellCount: row.goodsVirtualSellCount, 
    goodsDetailContent: row.goodsDetailContent, 
    goodsType: row.goodsType, 
    goodsSellStatus: row.goodsSellStatus, 
    goodsMultiSpecification: row.goodsMultiSpecification, 
    goodsAuditStatus: row.goodsAuditStatus,
    goodsNeedWriteOff: row.goodsNeedWriteOff,
  }).then(() => {
    ElMessage.success('提交审核成功')
    getGoodList()
  }).catch(err => {
    console.error('提交审核失败:', err)
    ElMessage.error('提交审核失败')
  })
}

// 删除原来的submitAudit函数
const submitAudit = () => {
  if (!confirmData.value || !confirmData.value.goodsId) {
    ElMessage.error('商品ID不能为空')
    return
  }

  axios.post('/goods/submit-audit', {
    goodsId: confirmData.value.goodsId
  }).then(() => {
    ElMessage.success('提交审核成功')
    getProductList()
  }).catch(err => {
    console.error('提交审核失败:', err)
    ElMessage.error('提交审核失败')
  })
}

// 下架商品
const handleTakeDown = (id,status) => {
  axios.put(`/goods/status/2`, {
    ids: id ? [id] : []
  }).then(() => {
    ElMessage.success('下架成功')
    getGoodList()
  })
}


// 删除商品
const handleDelete = (id) => {
  confirmMessage.value = '确认删除该商品吗？'
  confirmCallback.value = deleteProduct
  confirmData.value = { goodsId: id }
  confirmDialogRef.value.open()
}


// const handleOption = (index) => {
//   // console.log(index,'index')
//   if(index === '1'){
//     selectedId.value = ''
//     selectedstateId.value = ''
//     selectedValuecategories.value = ''
//     selectedValuemerchants.value = ''
//   }
//   state.currentPage = 1
//   getGoodList()
// }

// 获取列表方法
const getOrderList = () => {
  // state.loading = true

  // console.log(selectedstateId ,'index')
  axios.get('/merchants', {
    params: {
      pageNumber: 1,
      pageSize: 100,
      merchantName: '',
      status: ''
    }
  }).then(res => {
    optionsmerchants.value = res.list

  })
}

const getcategories = async () => {
  loadingcategories.value = true;
  axios.get('/categories', {
    params: {
      pageNumber: 1,
      pageSize: 100,
      // goodscategoryId:state.orderStatus,
      // goodsBelongMerchantId:state.orderStatus,
      // goodsBelongType:selectedId.value.length?0:selectedId.value,
      // goodssellstatus:selectedstateId.value.length?0:selectedstateId.value
    }
  }).then(res => {
    optionscategories.value  = res.list
    loadingcategories.value = false;
    
  })

}
// 获取轮播图列表
const getGoodList = () => {
  // console.log(selectedId.value,'selectedId.value')
  // if(selectedId.value.length === 0){
  //   selectedId.value = 0;
  // }
  // if(selectedstateId.value.length === 0){
  //   selectedstateId.value = 0;
  // }
  // console.log(selectedId.value,'selectedId.value')
  state.loading = true
  axios.get('/goods/list', {
    params: {
      pageNumber: state.currentPage,
      pageSize: state.pageSize,
      goodsBelongType:selectedId.value=== undefined?'':selectedId.value.length?'':selectedId.value,
      goodsSellStatus:selectedstateId.value=== undefined?'':selectedstateId.value.length?'':selectedstateId.value,
      goodscategoryId:selectedValuecategories.value=== undefined?'':selectedValuecategories.value.length?'':selectedValuecategories.value, 
      goodsBelongMerchantId:selectedValuemerchants.value=== undefined?'':selectedValuemerchants.value.length?'':selectedValuemerchants.value,
      goodsSupplierId:selectedSupplierId.value=== undefined?'':selectedSupplierId.value.length?'':selectedSupplierId.value,
      goodsName:state.searchName,
      goodsServiceFlag:0,
      // goodscategoryId:state.orderStatus,
      // goodsBelongMerchantId:state.orderStatus,
      // goodsBelongType:selectedId.value.length?0:selectedId.value,
      // goodssellstatus:selectedstateId.value.length?0:selectedstateId.value
    }
  }).then(res => {
    state.tableData = res.list
    state.total = res.totalCount
    state.currentPage = res.currPage
    state.loading = false
    goTop && goTop()
  })
}

const handleEditStock = (row) => {
  state.type = 'edit'
  addSwiper.value.open(row)
}

const handleAdd = () => {
  router.push({ path: '/add' })
}
const handleEdit = (id,option) => {
  router.push({ path: '/add', query: { id,option } })
}
const changePage = (val) => {
  state.currentPage = val
  getGoodList()
}
const handleStatus = (id, status) => {
  console.log(status,'status')
  const statustemp = ref(0); 
  if(status){
    statustemp.value = 1
  }else{
    statustemp.value = 0
  }
  console.log(statustemp.value,'statustemp')
  axios.put(`/goods/status/${statustemp.value}`, {
    ids: id ? [id] : []
  }).then(() => {
    ElMessage.success('修改成功')
    getGoodList()
  })
}
</script>

<style scoped>
  .good-container {
    min-height: 100%;
  }
  .el-card.is-always-shadow {
    min-height: 100%!important;
  }
</style>
